'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
    faShieldAlt,
    faLock,
    faUserCheck,
    faFingerprint,
    faArrowRight,
    faCertificate,
    faIdCard,
    faHeart,
    faGlobe,
    faRocket,
    faStar
} from '@fortawesome/free-solid-svg-icons';
import { useTheme } from '@/lib/context/ThemeContext';
import { ParallaxBackground } from '@/components/ui/ParallaxBackground';
import { CrefyButton } from '@/components/ui/CrefyButton';
import { CrefyNavbar } from '@/components/ui/CrefyNavbar';
import { AnimatedScrollContainer } from '@/components/ui/AnimatedScrollContainer';
import { MinimalHero } from '@/components/ui/MinimalHero';
import { ProjectCard, FeatureCard, MinimalCard } from '@/components/ui/MinimalCard';
import { GradientText } from '@/components/ui/AnimatedText';

export default function CrefyLandingPage() {
    const { theme } = useTheme();
    const router = useRouter();

    const handleGetStarted = () => {
        router.push('/login');
    };

    const handleLearnMore = () => {
        // Scroll to products section
        const productsSection = document.getElementById('products');
        if (productsSection) {
            productsSection.scrollIntoView({ behavior: 'smooth' });
        }
    };


    const products = [
        {
            icon: faIdCard,
            title: 'Crefy Passports',
            description: 'Reusable KYC solutions that streamline identity verification across platforms while maintaining user privacy and control.',
            features: ['One-time verification', 'Cross-platform compatibility', 'Privacy-preserving'],
            color: theme.colors.primary
        },
        {
            icon: faCertificate,
            title: 'Crefy Credentials',
            description: 'Digital certificates and qualifications that are verifiable, tamper-proof, and instantly shareable.',
            features: ['Instant verification', 'Tamper-proof', 'Globally recognized'],
            color: theme.colors.accent
        },
        {
            icon: faHeart,
            title: 'Crefy Memories',
            description: 'Event collectibles and digital memorabilia that capture and preserve special moments with cryptographic authenticity.',
            features: ['Authentic collectibles', 'Event participation', 'Digital memorabilia'],
            color: theme.colors.accentLight
        }
    ];

    const coreFeatures = [
        {
            icon: faShieldAlt,
            title: 'Identity Module',
            description: 'Secure, self-sovereign identity management with zero-knowledge proofs.'
        },
        {
            icon: faLock,
            title: 'Credentials Module',
            description: 'Verifiable credentials that are tamper-proof and instantly verifiable.'
        },
        {
            icon: faUserCheck,
            title: 'Attestations Module',
            description: 'Cryptographic attestations that provide trust and verification.'
        }
    ];

    return (
        <div className="min-h-screen">
            <CrefyNavbar />

            <ParallaxBackground orbCount={8} variant="subtle">
                {/* Minimal Hero Section */}
                <MinimalHero
                    title="Crefy"
                    subtitle="The Future of Digital Identity"
                    description="Revolutionary phygital platform bridging physical and digital worlds through modular infrastructure for decentralized identity, verifiable credentials, and authentic digital experiences."
                    primaryAction={{
                        text: "Get Started",
                        onClick: handleGetStarted
                    }}
                    secondaryAction={{
                        text: "Learn More",
                        onClick: handleLearnMore
                    }}
                    scrollTarget="products"
                />

                {/* Core Features Section - Simplified */}
                <section className="py-32 px-4">
                    <div className="max-w-6xl mx-auto">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                            {/* Left side - Features list */}
                            <div className="space-y-8">
                                <AnimatedScrollContainer animation="slideUp">
                                    <h3
                                        className="text-4xl md:text-5xl font-bold mb-8"
                                        style={{ color: theme.colors.text }}
                                    >
                                        Core Modules
                                    </h3>
                                </AnimatedScrollContainer>

                                {coreFeatures.map((feature, index) => (
                                    <FeatureCard
                                        key={index}
                                        icon={
                                            <FontAwesomeIcon
                                                icon={feature.icon}
                                                size="lg"
                                            />
                                        }
                                        title={feature.title}
                                        description={feature.description}
                                        delay={index * 0.1}
                                    />
                                ))}
                            </div>

                            {/* Right side - Visual element */}
                            <div className="relative">
                                <AnimatedScrollContainer animation="fadeIn" delay={0.3}>
                                    <MinimalCard
                                        variant="bordered"
                                        padding="xl"
                                        className="text-center relative overflow-hidden"
                                    >
                                        {/* Background gradient */}
                                        <div
                                            className="absolute inset-0 opacity-5"
                                            style={{
                                                background: `radial-gradient(circle, ${theme.colors.primary} 0%, transparent 70%)`
                                            }}
                                        />

                                        <div className="relative z-10">
                                            <div
                                                className="w-24 h-24 mx-auto mb-6 rounded-3xl flex items-center justify-center"
                                                style={{ backgroundColor: theme.colors.primary }}
                                            >
                                                <FontAwesomeIcon
                                                    icon={faFingerprint}
                                                    size="3x"
                                                    className="text-white"
                                                />
                                            </div>
                                            <h4
                                                className="text-2xl font-bold mb-4"
                                                style={{ color: theme.colors.text }}
                                            >
                                                Secure by Design
                                            </h4>
                                            <p
                                                className="text-lg leading-relaxed"
                                                style={{ color: theme.colors.secondaryText }}
                                            >
                                                Built on battle-tested cryptographic primitives and blockchain technology
                                                to ensure the highest levels of security and privacy.
                                            </p>
                                        </div>
                                    </MinimalCard>
                                </AnimatedScrollContainer>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Products Section - Redesigned with Minimal Approach */}
                <section id="products" className="py-32 px-4">
                    <div className="max-w-7xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="text-center mb-20">
                                <GradientText
                                    text="Our Products"
                                    className="text-5xl md:text-6xl font-bold mb-8"
                                    gradient={`linear-gradient(135deg, ${theme.colors.text} 0%, ${theme.colors.primary} 100%)`}
                                />
                                <p
                                    className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Three innovative products built on our modular infrastructure,
                                    each designed to solve specific identity and credential challenges.
                                </p>
                            </div>
                        </AnimatedScrollContainer>

                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
                            {products.map((product, index) => (
                                <ProjectCard
                                    key={index}
                                    title={product.title}
                                    description={product.description}
                                    features={product.features}
                                    icon={
                                        <FontAwesomeIcon
                                            icon={product.icon}
                                            size="lg"
                                        />
                                    }
                                    onClick={() => router.push('/login')}
                                    delay={index * 0.1}
                                    accentColor={product.color}
                                />
                            ))}
                        </div>
                    </div>
                </section>

                {/* Why Choose Crefy - Simplified Section */}
                <section className="py-32 px-4">
                    <div className="max-w-6xl mx-auto">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="text-center mb-20">
                                <GradientText
                                    text="Why Choose Crefy?"
                                    className="text-5xl md:text-6xl font-bold mb-8"
                                    gradient={`linear-gradient(135deg, ${theme.colors.text} 0%, ${theme.colors.primary} 100%)`}
                                />
                                <p
                                    className="text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Experience the next generation of digital identity with cutting-edge technology
                                    and privacy-first architecture.
                                </p>
                            </div>
                        </AnimatedScrollContainer>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            {[
                                {
                                    icon: faGlobe,
                                    title: 'Global Reach',
                                    description: 'Seamlessly interact with partner companies worldwide through unified identity verification.'
                                },
                                {
                                    icon: faRocket,
                                    title: 'Innovation First',
                                    description: 'Cutting-edge technology powered by zero-knowledge proofs and blockchain infrastructure.'
                                },
                                {
                                    icon: faShieldAlt,
                                    title: 'Privacy-First',
                                    description: 'Your data remains under your control with selective disclosure and minimal data sharing.'
                                },
                                {
                                    icon: faStar,
                                    title: 'Premium Experience',
                                    description: 'Enjoy a seamless, intuitive interface designed for both technical and non-technical users.'
                                }
                            ].map((benefit, index) => (
                                <FeatureCard
                                    key={index}
                                    icon={
                                        <FontAwesomeIcon
                                            icon={benefit.icon}
                                            size="lg"
                                        />
                                    }
                                    title={benefit.title}
                                    description={benefit.description}
                                    delay={index * 0.1}
                                />
                            ))}
                        </div>
                    </div>
                </section>

                {/* Call to Action Section - Minimal Design */}
                <section className="py-32 px-4">
                    <div className="max-w-5xl mx-auto text-center">
                        <AnimatedScrollContainer animation="slideUp">
                            <div className="space-y-8">
                                <GradientText
                                    text="Ready to Get Started?"
                                    className="text-5xl md:text-6xl lg:text-7xl font-bold"
                                    gradient={`linear-gradient(135deg, ${theme.colors.text} 0%, ${theme.colors.primary} 50%, ${theme.colors.accent} 100%)`}
                                />
                                <p
                                    className="text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed"
                                    style={{ color: theme.colors.secondaryText }}
                                >
                                    Join the future of decentralized identity. Connect with our partner
                                    companies and experience secure, private digital interactions.
                                </p>
                                <div className="pt-8">
                                    <CrefyButton
                                        variant="primary"
                                        size="xl"
                                        onClick={handleGetStarted}
                                        className="min-w-[300px]"
                                    >
                                        Access Partner Companies
                                        <FontAwesomeIcon icon={faArrowRight} className="ml-3" />
                                    </CrefyButton>
                                </div>
                            </div>
                        </AnimatedScrollContainer>
                    </div>
                </section>
            </ParallaxBackground>
        </div>
    );
}